import 'package:flutter/material.dart';
import '../utils/callback_interpreter.dart';

/// Extension on Color to provide hex string conversion
extension ColorExtension on Color {
  /// Converts a Color to a hex string (without the # prefix)
  String toHexString() {
    return '${r.round().toRadixString(16).padLeft(2, '0')}${g.round().toRadixString(16).padLeft(2, '0')}${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// A customizable multi-select widget that allows users to select multiple options from a list.
///
/// This widget provides a rich set of customization options for multi-select functionality,
/// including styling, validation, search, and more.
class MultiSelectWidget extends StatefulWidget {
  /// The list of available options to select from.
  final List<String> options;

  /// The initially selected options.
  final List<String>? initialValues;

  /// The hint text to show when no options are selected.
  final String? hintText;

  /// The label text to show above the input.
  final String? labelText;

  /// The helper text to show below the input.
  final String? helperText;

  /// The error text to show when validation fails.
  final String? errorText;

  /// The minimum number of selections required.
  final int? minSelections;

  /// The maximum number of selections allowed.
  final int? maxSelections;

  /// Whether the input is required.
  final bool isRequired;

  /// Whether the input is read-only.
  final bool readOnly;

  /// Whether the input is disabled.
  final bool isDisabled;

  /// Whether to auto-validate the input.
  final bool autoValidate;

  /// Whether to show a search field to filter options.
  final bool showSearch;

  /// The placeholder text for the search field.
  final String searchHintText;

  /// Whether to show checkboxes next to options.
  final bool showCheckboxes;

  /// Whether to show selected options as chips.
  final bool showChips;

  /// Whether to show a clear button to clear all selections.
  final bool showClearButton;

  /// Whether to show a select all button.
  final bool showSelectAllButton;

  /// The text to display for the select all button.
  final String selectAllText;

  /// The text to display for the clear button.
  final String clearAllText;

  /// The text style of the options.
  final TextStyle? optionTextStyle;

  /// The text style of the selected options.
  final TextStyle? selectedOptionTextStyle;

  /// The text style of the label.
  final TextStyle? labelStyle;

  /// The text style of the hint.
  final TextStyle? hintStyle;

  /// The text style of the helper text.
  final TextStyle? helperStyle;

  /// The text style of the error text.
  final TextStyle? errorStyle;

  /// The text style of the search field.
  final TextStyle? searchTextStyle;

  /// The color of the option text.
  final Color? optionTextColor;

  /// The color of the selected option text.
  final Color? selectedOptionTextColor;

  /// The color of the label text.
  final Color? labelColor;

  /// The color of the hint text.
  final Color? hintColor;

  /// The color of the helper text.
  final Color? helperColor;

  /// The color of the error text.
  final Color? errorColor;

  /// The color of the search text.
  final Color? searchTextColor;

  /// The color of the option background.
  final Color? optionBackgroundColor;

  /// The color of the selected option background.
  final Color selectedOptionBackgroundColor;

  /// The color of the dropdown background.
  final Color? dropdownBackgroundColor;

  /// The color of the input background.
  final Color? backgroundColor;

  /// The color of the border.
  final Color? borderColor;

  /// The color of the focused border.
  final Color focusedBorderColor;

  /// The color of the error border.
  final Color errorBorderColor;

  /// The color of the chip background.
  final Color? chipBackgroundColor;

  /// The color of the chip text.
  final Color? chipTextColor;

  /// The color of the chip delete icon.
  final Color? chipDeleteIconColor;

  /// The width of the border.
  final double borderWidth;

  /// The width of the focused border.
  final double focusedBorderWidth;

  /// The width of the error border.
  final double errorBorderWidth;

  /// The radius of the border corners.
  final double borderRadius;

  /// The radius of the chip corners.
  final double chipBorderRadius;

  /// The width of the dropdown.
  final double? width;

  /// The height of the input field.
  final double? height;

  /// The maximum height of the dropdown.
  final double? maxDropdownHeight;

  /// The padding around the input.
  final EdgeInsetsGeometry padding;

  /// The margin around the input.
  final EdgeInsetsGeometry margin;

  /// The padding around each option.
  final EdgeInsetsGeometry optionPadding;

  /// The padding around each chip.
  final EdgeInsetsGeometry chipPadding;

  /// The icon to show for the dropdown button.
  final IconData dropdownIcon;

  /// The icon to show for the search field.
  final IconData searchIcon;

  /// The icon to show for the clear button.
  final IconData clearIcon;

  /// The icon to show for the select all button.
  final IconData selectAllIcon;

  /// The icon to show for the chip delete button.
  final IconData chipDeleteIcon;

  /// The color of the dropdown icon.
  final Color? dropdownIconColor;

  /// The color of the search icon.
  final Color? searchIconColor;

  /// The color of the clear icon.
  final Color? clearIconColor;

  /// The color of the select all icon.
  final Color? selectAllIconColor;

  /// The size of the dropdown icon.
  final double dropdownIconSize;

  /// The size of the search icon.
  final double searchIconSize;

  /// The size of the clear icon.
  final double clearIconSize;

  /// The size of the select all icon.
  final double selectAllIconSize;

  /// The size of the chip delete icon.
  final double chipDeleteIconSize;

  /// Whether to show a shadow under the input.
  final bool hasShadow;

  /// The elevation of the shadow.
  final double elevation;

  /// The color of the shadow.
  final Color? shadowColor;

  /// The elevation of the dropdown.
  final double dropdownElevation;

  /// The callback to execute when the selection changes.
  final Function(List<String> values)? onChanged;

  /// The callback to execute when the input is validated.
  final bool Function(List<String> values)? validator;

  /// The callback to execute when the clear button is pressed.
  final VoidCallback? onClear;

  /// The callback to execute when the select all button is pressed.
  final VoidCallback? onSelectAll;

  /// The callback to execute when the input is tapped.
  final VoidCallback? onTap;

  /// The display mode for the multi-select widget.
  final MultiSelectDisplayMode displayMode;

  /// The sort mode for the options.
  final MultiSelectSortMode sortMode;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use for the border when the widget is hovered
  final Color hoverBorderColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Animation properties
  /// Whether to animate the widget when it changes
  final bool hasAnimation;

  /// Duration of the animation
  final Duration animationDuration;

  /// Curve to use for the animation
  final Curve animationCurve;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // MultiSelect-specific JSON configuration
  /// Whether to use JSON multi-select configuration
  final bool useJsonMultiSelectConfig;

  /// Multi-select-specific JSON configuration
  final Map<String, dynamic>? multiSelectConfig;

  /// Creates a multi-select widget.
  const MultiSelectWidget({
    super.key,
    required this.options,
    this.initialValues,
    this.hintText = 'Select options',
    this.labelText,
    this.helperText,
    this.errorText,
    this.minSelections,
    this.maxSelections,
    this.isRequired = false,
    this.readOnly = false,
    this.isDisabled = false,
    this.autoValidate = false,
    this.showSearch = false,
    this.searchHintText = 'Search options',
    this.showCheckboxes = true,
    this.showChips = true,
    this.showClearButton = true,
    this.showSelectAllButton = false,
    this.selectAllText = 'Select All',
    this.clearAllText = 'Clear All',
    this.optionTextStyle,
    this.selectedOptionTextStyle,
    this.labelStyle,
    this.hintStyle,
    this.helperStyle,
    this.errorStyle,
    this.searchTextStyle,
    this.optionTextColor,
    this.selectedOptionTextColor,
    this.labelColor,
    this.hintColor,
    this.helperColor,
    this.errorColor,
    this.searchTextColor,
    this.optionBackgroundColor,
    this.selectedOptionBackgroundColor = const Color(
      0xFF0058FF,
    ), // Blue selected option background
    this.dropdownBackgroundColor,
    this.backgroundColor = const Color(
      0xFFF5F5F5,
    ), // Light grey background (default)
    this.borderColor,
    this.focusedBorderColor = const Color(0xFF0058FF), // Blue focused border
    this.errorBorderColor = const Color(0xFFCCCCCC), // Light grey error border
    this.chipBackgroundColor,
    this.chipTextColor,
    this.chipDeleteIconColor,
    this.borderWidth = 1.0,
    this.focusedBorderWidth = 2.0,
    this.errorBorderWidth = 1.0,
    this.borderRadius = 4.0,
    this.chipBorderRadius = 4.0, // Reduced border radius to match image
    this.width,
    this.height,
    this.maxDropdownHeight,
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
    this.margin = EdgeInsets.zero,
    this.optionPadding = const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 4.0,
    ),
    this.chipPadding = const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 4.0,
    ),
    this.dropdownIcon = Icons.arrow_drop_down,
    this.searchIcon = Icons.search,
    this.clearIcon = Icons.clear,
    this.selectAllIcon = Icons.select_all,
    this.chipDeleteIcon = Icons.cancel,
    this.dropdownIconColor= const Color(0xFFCCCCCC),
    this.searchIconColor,
    this.clearIconColor,
    this.selectAllIconColor,
    this.dropdownIconSize = 24.0,
    this.searchIconSize = 20.0,
    this.clearIconSize = 20.0,
    this.selectAllIconSize = 20.0,
    this.chipDeleteIconSize = 16.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.shadowColor,
    this.dropdownElevation = 8.0,
    this.onChanged,
    this.validator,
    this.onClear,
    this.onSelectAll,
    this.onTap,
    this.displayMode = MultiSelectDisplayMode.menu,
    this.sortMode = MultiSelectSortMode.none,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.hoverBorderColor = const Color(0xFF0058FF), // Blue hover border
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Animation properties
    this.hasAnimation = false,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // MultiSelect-specific JSON configuration
    this.useJsonMultiSelectConfig = false,
    this.multiSelectConfig,
  });

  /// Creates a MultiSelectWidget from a JSON map
  ///
  /// This factory constructor allows for creating a MultiSelectWidget from a JSON map,
  /// making it easy to configure the widget from API responses or configuration files.
  factory MultiSelectWidget.fromJson(Map<String, dynamic> json) {
    // Parse display mode
    MultiSelectDisplayMode displayMode = MultiSelectDisplayMode.menu;
    if (json.containsKey('displayMode')) {
      final String mode = json['displayMode'].toString().toLowerCase();
      if (mode == 'dialog') {
        displayMode = MultiSelectDisplayMode.dialog;
      } else if (mode == 'bottomsheet') {
        displayMode = MultiSelectDisplayMode.bottomSheet;
      } else if (mode == 'chipinput') {
        displayMode = MultiSelectDisplayMode.chipInput;
      }
    }

    // Parse sort mode
    MultiSelectSortMode sortMode = MultiSelectSortMode.none;
    if (json.containsKey('sortMode')) {
      final String mode = json['sortMode'].toString().toLowerCase();
      if (mode == 'alphabetical') {
        sortMode = MultiSelectSortMode.alphabetical;
      } else if (mode == 'selectedfirst') {
        sortMode = MultiSelectSortMode.selectedFirst;
      } else if (mode == 'selectedlast') {
        sortMode = MultiSelectSortMode.selectedLast;
      }
    }

    // Parse options and initial values
    List<String> options = [];
    if (json.containsKey('options') && json['options'] is List) {
      options = List<String>.from(
        json['options'].map((item) => item.toString()),
      );
    }

    List<String>? initialValues;
    if (json.containsKey('initialValues') && json['initialValues'] is List) {
      initialValues = List<String>.from(
        json['initialValues'].map((item) => item.toString()),
      );
    }

    // Parse colors
    Color? backgroundColor;
    if (json.containsKey('backgroundColor')) {
      backgroundColor = _parseColor(json['backgroundColor']);
    }

    Color? borderColor;
    if (json.containsKey('borderColor')) {
      borderColor = _parseColor(json['borderColor']);
    }

    Color? focusedBorderColor;
    if (json.containsKey('focusedBorderColor')) {
      focusedBorderColor = _parseColor(json['focusedBorderColor']);
    }

    Color? errorBorderColor;
    if (json.containsKey('errorBorderColor')) {
      errorBorderColor = _parseColor(json['errorBorderColor']);
    }

    Color? chipBackgroundColor;
    if (json.containsKey('chipBackgroundColor')) {
      chipBackgroundColor = _parseColor(json['chipBackgroundColor']);
    }

    Color? chipTextColor;
    if (json.containsKey('chipTextColor')) {
      chipTextColor = _parseColor(json['chipTextColor']);
    }

    Color? chipDeleteIconColor;
    if (json.containsKey('chipDeleteIconColor')) {
      chipDeleteIconColor = _parseColor(json['chipDeleteIconColor']);
    }

    Color? optionTextColor;
    if (json.containsKey('optionTextColor')) {
      optionTextColor = _parseColor(json['optionTextColor']);
    }

    Color? selectedOptionTextColor;
    if (json.containsKey('selectedOptionTextColor')) {
      selectedOptionTextColor = _parseColor(json['selectedOptionTextColor']);
    }

    Color? labelColor;
    if (json.containsKey('labelColor')) {
      labelColor = _parseColor(json['labelColor']);
    }

    Color? hintColor;
    if (json.containsKey('hintColor')) {
      hintColor = _parseColor(json['hintColor']);
    }

    Color? helperColor;
    if (json.containsKey('helperColor')) {
      helperColor = _parseColor(json['helperColor']);
    }

    Color? errorColor;
    if (json.containsKey('errorColor')) {
      errorColor = _parseColor(json['errorColor']);
    }

    Color? searchTextColor;
    if (json.containsKey('searchTextColor')) {
      searchTextColor = _parseColor(json['searchTextColor']);
    }

    Color? optionBackgroundColor;
    if (json.containsKey('optionBackgroundColor')) {
      optionBackgroundColor = _parseColor(json['optionBackgroundColor']);
    }

    Color? selectedOptionBackgroundColor;
    if (json.containsKey('selectedOptionBackgroundColor')) {
      selectedOptionBackgroundColor = _parseColor(
        json['selectedOptionBackgroundColor'],
      );
    }

    Color? dropdownBackgroundColor;
    if (json.containsKey('dropdownBackgroundColor')) {
      dropdownBackgroundColor = _parseColor(json['dropdownBackgroundColor']);
    }

    Color? dropdownIconColor;
    if (json.containsKey('dropdownIconColor')) {
      dropdownIconColor = _parseColor(json['dropdownIconColor']);
    }

    Color? searchIconColor;
    if (json.containsKey('searchIconColor')) {
      searchIconColor = _parseColor(json['searchIconColor']);
    }

    Color? clearIconColor;
    if (json.containsKey('clearIconColor')) {
      clearIconColor = _parseColor(json['clearIconColor']);
    }

    Color? selectAllIconColor;
    if (json.containsKey('selectAllIconColor')) {
      selectAllIconColor = _parseColor(json['selectAllIconColor']);
    }

    Color? shadowColor;
    if (json.containsKey('shadowColor')) {
      shadowColor = _parseColor(json['shadowColor']);
    }

    Color? hoverColor;
    if (json.containsKey('hoverColor')) {
      hoverColor = _parseColor(json['hoverColor']);
    }

    Color? hoverBorderColor;
    if (json.containsKey('hoverBorderColor')) {
      hoverBorderColor = _parseColor(json['hoverBorderColor']);
    }

    Color? focusColor;
    if (json.containsKey('focusColor')) {
      focusColor = _parseColor(json['focusColor']);
    }

    // Parse padding and margin
    EdgeInsetsGeometry padding = const EdgeInsets.symmetric(
      horizontal: 12.0,
      vertical: 8.0,
    );
    if (json.containsKey('padding')) {
      padding = _parseEdgeInsets(json['padding']);
    }

    EdgeInsetsGeometry margin = EdgeInsets.zero;
    if (json.containsKey('margin')) {
      margin = _parseEdgeInsets(json['margin']);
    }

    EdgeInsetsGeometry optionPadding = const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 12.0,
    );
    if (json.containsKey('optionPadding')) {
      optionPadding = _parseEdgeInsets(json['optionPadding']);
    }

    EdgeInsetsGeometry chipPadding = const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 4.0,
    );
    if (json.containsKey('chipPadding')) {
      chipPadding = _parseEdgeInsets(json['chipPadding']);
    }

    // Parse icons
    IconData dropdownIcon = Icons.arrow_drop_down;
    if (json.containsKey('dropdownIcon')) {
      dropdownIcon =
          _parseIconData(json['dropdownIcon']) ?? Icons.arrow_drop_down;
    }

    IconData searchIcon = Icons.search;
    if (json.containsKey('searchIcon')) {
      searchIcon = _parseIconData(json['searchIcon']) ?? Icons.search;
    }

    IconData clearIcon = Icons.clear;
    if (json.containsKey('clearIcon')) {
      clearIcon = _parseIconData(json['clearIcon']) ?? Icons.clear;
    }

    IconData selectAllIcon = Icons.select_all;
    if (json.containsKey('selectAllIcon')) {
      selectAllIcon = _parseIconData(json['selectAllIcon']) ?? Icons.select_all;
    }

    IconData chipDeleteIcon = Icons.cancel;
    if (json.containsKey('chipDeleteIcon')) {
      chipDeleteIcon = _parseIconData(json['chipDeleteIcon']) ?? Icons.cancel;
    }

    // Parse text styles
    TextStyle? optionTextStyle;
    if (json.containsKey('optionTextStyle')) {
      optionTextStyle = _parseTextStyle(json['optionTextStyle']);
    }

    TextStyle? selectedOptionTextStyle;
    if (json.containsKey('selectedOptionTextStyle')) {
      selectedOptionTextStyle = _parseTextStyle(
        json['selectedOptionTextStyle'],
      );
    }

    TextStyle? labelStyle;
    if (json.containsKey('labelStyle')) {
      labelStyle = _parseTextStyle(json['labelStyle']);
    }

    TextStyle? hintStyle;
    if (json.containsKey('hintStyle')) {
      hintStyle = _parseTextStyle(json['hintStyle']);
    }

    TextStyle? helperStyle;
    if (json.containsKey('helperStyle')) {
      helperStyle = _parseTextStyle(json['helperStyle']);
    }

    TextStyle? errorStyle;
    if (json.containsKey('errorStyle')) {
      errorStyle = _parseTextStyle(json['errorStyle']);
    }

    TextStyle? searchTextStyle;
    if (json.containsKey('searchTextStyle')) {
      searchTextStyle = _parseTextStyle(json['searchTextStyle']);
    }

    // Parse animation properties
    Duration animationDuration = const Duration(milliseconds: 300);
    if (json.containsKey('animationDuration')) {
      animationDuration = Duration(
        milliseconds: json['animationDuration'] as int? ?? 300,
      );
    }

    Curve animationCurve = Curves.easeInOut;
    if (json.containsKey('animationCurve')) {
      final String curve = json['animationCurve'].toString().toLowerCase();
      if (curve == 'linear') {
        animationCurve = Curves.linear;
      } else if (curve == 'decelerate') {
        animationCurve = Curves.decelerate;
      } else if (curve == 'ease') {
        animationCurve = Curves.ease;
      } else if (curve == 'easein') {
        animationCurve = Curves.easeIn;
      } else if (curve == 'easeout') {
        animationCurve = Curves.easeOut;
      } else if (curve == 'elasticin') {
        animationCurve = Curves.elasticIn;
      } else if (curve == 'elasticout') {
        animationCurve = Curves.elasticOut;
      } else if (curve == 'elasticinout') {
        animationCurve = Curves.elasticInOut;
      }
    }

    return MultiSelectWidget(
      // Basic properties
      options: options,
      initialValues: initialValues,
      hintText: json['hintText'] as String? ?? 'Select options',
      labelText: json['labelText'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      minSelections: json['minSelections'] as int?,
      maxSelections: json['maxSelections'] as int?,
      isRequired: json['isRequired'] as bool? ?? false,
      readOnly: json['readOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      autoValidate: json['autoValidate'] as bool? ?? false,

      // Display properties
      displayMode: displayMode,
      sortMode: sortMode,
      showSearch: json['showSearch'] as bool? ?? false,
      searchHintText: json['searchHintText'] as String? ?? 'Search options',
      showCheckboxes: json['showCheckboxes'] as bool? ?? true,
      showChips: json['showChips'] as bool? ?? true,
      showClearButton: json['showClearButton'] as bool? ?? true,
      showSelectAllButton: json['showSelectAllButton'] as bool? ?? false,
      selectAllText: json['selectAllText'] as String? ?? 'Select All',
      clearAllText: json['clearAllText'] as String? ?? 'Clear All',

      // Style properties
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      maxDropdownHeight:
          json['maxDropdownHeight'] != null
              ? (json['maxDropdownHeight'] as num).toDouble()
              : null,
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      focusedBorderWidth:
          json['focusedBorderWidth'] != null
              ? (json['focusedBorderWidth'] as num).toDouble()
              : 2.0,
      errorBorderWidth:
          json['errorBorderWidth'] != null
              ? (json['errorBorderWidth'] as num).toDouble()
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      chipBorderRadius:
          json['chipBorderRadius'] != null
              ? (json['chipBorderRadius'] as num).toDouble()
              : 4.0, // Reduced border radius to match image
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      dropdownElevation:
          json['dropdownElevation'] != null
              ? (json['dropdownElevation'] as num).toDouble()
              : 8.0,
      dropdownIconSize:
          json['dropdownIconSize'] != null
              ? (json['dropdownIconSize'] as num).toDouble()
              : 24.0,
      searchIconSize:
          json['searchIconSize'] != null
              ? (json['searchIconSize'] as num).toDouble()
              : 20.0,
      clearIconSize:
          json['clearIconSize'] != null
              ? (json['clearIconSize'] as num).toDouble()
              : 20.0,
      selectAllIconSize:
          json['selectAllIconSize'] != null
              ? (json['selectAllIconSize'] as num).toDouble()
              : 20.0,
      chipDeleteIconSize:
          json['chipDeleteIconSize'] != null
              ? (json['chipDeleteIconSize'] as num).toDouble()
              : 16.0,

      // Color properties
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      focusedBorderColor: focusedBorderColor ?? const Color(0xFF0058FF),
      errorBorderColor: errorBorderColor ?? const Color(0xFFCCCCCC),
      chipBackgroundColor: chipBackgroundColor,
      chipTextColor: chipTextColor,
      chipDeleteIconColor: chipDeleteIconColor,
      optionTextColor: optionTextColor,
      selectedOptionTextColor: selectedOptionTextColor,
      labelColor: labelColor,
      hintColor: hintColor,
      helperColor: helperColor,
      errorColor: errorColor,
      searchTextColor: searchTextColor,
      optionBackgroundColor: optionBackgroundColor,
      selectedOptionBackgroundColor:
          selectedOptionBackgroundColor ?? const Color(0xFF0058FF),
      dropdownBackgroundColor: dropdownBackgroundColor,
      dropdownIconColor: dropdownIconColor,
      searchIconColor: searchIconColor,
      clearIconColor: clearIconColor,
      selectAllIconColor: selectAllIconColor,
      shadowColor: shadowColor,

      // Icon properties
      dropdownIcon: dropdownIcon,
      searchIcon: searchIcon,
      clearIcon: clearIcon,
      selectAllIcon: selectAllIcon,
      chipDeleteIcon: chipDeleteIcon,

      // Text style properties
      optionTextStyle: optionTextStyle,
      selectedOptionTextStyle: selectedOptionTextStyle,
      labelStyle: labelStyle,
      hintStyle: hintStyle,
      helperStyle: helperStyle,
      errorStyle: errorStyle,
      searchTextStyle: searchTextStyle,

      // Layout properties
      padding: padding,
      margin: margin,
      optionPadding: optionPadding,
      chipPadding: chipPadding,

      // Advanced interaction properties
      onHover: json.containsKey('onHover') ? (_) {} : null,
      onFocus: json.containsKey('onFocus') ? (_) {} : null,
      autofocus: json['autofocus'] as bool? ?? false,
      hoverColor: hoverColor,
      hoverBorderColor: hoverBorderColor ?? const Color(0xFF0058FF),
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onDoubleTap: json.containsKey('onDoubleTap') ? () {} : null,
      onLongPress: json.containsKey('onLongPress') ? () {} : null,

      // Animation properties
      hasAnimation: json['hasAnimation'] as bool? ?? false,
      animationDuration: animationDuration,
      animationCurve: animationCurve,

      // Accessibility properties
      semanticsLabel: json['semanticsLabel'] as String?,
      excludeFromSemantics: json['excludeFromSemantics'] as bool? ?? false,

      // JSON configuration properties
      jsonCallbacks:
          json.containsKey('callbacks')
              ? json['callbacks'] as Map<String, dynamic>
              : null,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      callbackState:
          json.containsKey('callbackState')
              ? json['callbackState'] as Map<String, dynamic>
              : null,
      jsonConfig: json,
      useJsonStyling: json['useJsonStyling'] as bool? ?? false,
      useJsonFormatting: json['useJsonFormatting'] as bool? ?? false,
      useJsonMultiSelectConfig:
          json['useJsonMultiSelectConfig'] as bool? ?? false,
      multiSelectConfig:
          json.containsKey('multiSelectConfig')
              ? json['multiSelectConfig'] as Map<String, dynamic>
              : null,
    );
  }

  /// Parses a color from a string or map
  static Color _parseColor(dynamic colorValue) {
    if (colorValue is String) {
      if (colorValue.startsWith('#')) {
        // Parse hex color
        String hex = colorValue.replaceFirst('#', '');
        if (hex.length == 3) {
          // Convert 3-digit hex to 6-digit
          hex = hex.split('').map((char) => char + char).join('');
        }
        if (hex.length == 6) {
          hex = 'FF$hex'; // Add alpha channel if not present
        }
        return Color(int.parse(hex, radix: 16));
      } else {
        // Parse named color
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Color(0xFF0058FF);
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            return Colors.blue;
        }
      }
    } else if (colorValue is Map) {
      // Parse RGBA color
      final int r = colorValue['r'] as int? ?? 0;
      final int g = colorValue['g'] as int? ?? 0;
      final int b = colorValue['b'] as int? ?? 0;
      final double a =
          colorValue['a'] != null ? (colorValue['a'] as num).toDouble() : 1.0;
      return Color.fromRGBO(r, g, b, a);
    }
    return const Color(0xFF0058FF); // Default color
  }

  /// Parses edge insets from a map or number
  static EdgeInsetsGeometry _parseEdgeInsets(dynamic value) {
    if (value is num) {
      return EdgeInsets.all(value.toDouble());
    } else if (value is Map) {
      if (value.containsKey('all')) {
        return EdgeInsets.all((value['all'] as num).toDouble());
      } else if (value.containsKey('horizontal') ||
          value.containsKey('vertical')) {
        return EdgeInsets.symmetric(
          horizontal:
              value.containsKey('horizontal')
                  ? (value['horizontal'] as num).toDouble()
                  : 0.0,
          vertical:
              value.containsKey('vertical')
                  ? (value['vertical'] as num).toDouble()
                  : 0.0,
        );
      } else {
        return EdgeInsets.fromLTRB(
          value.containsKey('left') ? (value['left'] as num).toDouble() : 0.0,
          value.containsKey('top') ? (value['top'] as num).toDouble() : 0.0,
          value.containsKey('right') ? (value['right'] as num).toDouble() : 0.0,
          value.containsKey('bottom')
              ? (value['bottom'] as num).toDouble()
              : 0.0,
        );
      }
    }
    return const EdgeInsets.symmetric(
      horizontal: 12.0,
      vertical: 8.0,
    ); // Default padding
  }

  /// Parses icon data from a string
  static IconData? _parseIconData(String value) {
    switch (value.toLowerCase()) {
      case 'arrow_drop_down':
        return Icons.arrow_drop_down;
      case 'arrow_drop_up':
        return Icons.arrow_drop_up;
      case 'arrow_down':
        return Icons.arrow_downward;
      case 'arrow_up':
        return Icons.arrow_upward;
      case 'search':
        return Icons.search;
      case 'clear':
        return Icons.clear;
      case 'cancel':
        return Icons.cancel;
      case 'select_all':
        return Icons.select_all;
      case 'check':
        return Icons.check;
      case 'check_circle':
        return Icons.check_circle;
      case 'check_box':
        return Icons.check_box;
      case 'check_box_outline_blank':
        return Icons.check_box_outline_blank;
      case 'add':
        return Icons.add;
      case 'remove':
        return Icons.remove;
      case 'delete':
        return Icons.delete;
      case 'edit':
        return Icons.edit;
      case 'settings':
        return Icons.settings;
      case 'info':
        return Icons.info;
      case 'help':
        return Icons.help;
      case 'warning':
        return Icons.warning;
      case 'error':
        return Icons.error;
      case 'menu':
        return Icons.menu;
      case 'more':
        return Icons.more_vert;
      case 'more_horiz':
        return Icons.more_horiz;
      case 'expand':
        return Icons.expand_more;
      case 'collapse':
        return Icons.expand_less;
      case 'filter':
        return Icons.filter_list;
      case 'sort':
        return Icons.sort;
      default:
        return null;
    }
  }

  /// Parses a text style from a map
  static TextStyle? _parseTextStyle(dynamic value) {
    if (value is! Map) return null;

    final Map<String, dynamic> styleMap = value as Map<String, dynamic>;

    Color? color;
    if (styleMap.containsKey('color')) {
      color = _parseColor(styleMap['color']);
    }

    double? fontSize;
    if (styleMap.containsKey('fontSize')) {
      fontSize = (styleMap['fontSize'] as num).toDouble();
    }

    FontWeight? fontWeight;
    if (styleMap.containsKey('fontWeight')) {
      fontWeight = _parseFontWeight(styleMap['fontWeight']);
    }

    FontStyle? fontStyle;
    if (styleMap.containsKey('fontStyle')) {
      fontStyle = _parseFontStyle(styleMap['fontStyle']);
    }

    TextDecoration? decoration;
    if (styleMap.containsKey('decoration')) {
      decoration = _parseTextDecoration(styleMap['decoration']);
    }

    return TextStyle(
      color: color,
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontStyle: fontStyle,
      decoration: decoration,
    );
  }

  /// Parses font weight from a string or number
  static FontWeight _parseFontWeight(dynamic value) {
    if (value is String) {
      switch (value.toLowerCase()) {
        case 'thin':
          return FontWeight.w100;
        case 'extralight':
          return FontWeight.w200;
        case 'light':
          return FontWeight.w300;
        case 'regular':
          return FontWeight.w400;
        case 'medium':
          return FontWeight.w500;
        case 'semibold':
          return FontWeight.w600;
        case 'bold':
          return FontWeight.w700;
        case 'extrabold':
          return FontWeight.w800;
        case 'black':
          return FontWeight.w900;
        default:
          return FontWeight.normal;
      }
    } else if (value is int) {
      switch (value) {
        case 100:
          return FontWeight.w100;
        case 200:
          return FontWeight.w200;
        case 300:
          return FontWeight.w300;
        case 400:
          return FontWeight.w400;
        case 500:
          return FontWeight.w500;
        case 600:
          return FontWeight.w600;
        case 700:
          return FontWeight.w700;
        case 800:
          return FontWeight.w800;
        case 900:
          return FontWeight.w900;
        default:
          return FontWeight.normal;
      }
    }
    return FontWeight.normal; // Default font weight
  }

  /// Parses font style from a string
  static FontStyle _parseFontStyle(String value) {
    switch (value.toLowerCase()) {
      case 'italic':
        return FontStyle.italic;
      default:
        return FontStyle.normal;
    }
  }

  /// Parses text decoration from a string
  static TextDecoration _parseTextDecoration(String value) {
    switch (value.toLowerCase()) {
      case 'underline':
        return TextDecoration.underline;
      case 'overline':
        return TextDecoration.overline;
      case 'linethrough':
        return TextDecoration.lineThrough;
      default:
        return TextDecoration.none;
    }
  }

  /// Converts the widget to a JSON map
  ///
  /// This method allows for serialization of the widget's configuration,
  /// making it easy to save and restore widget state.
  Map<String, dynamic> toJson() {
    return {
      // Basic properties
      'options': options,
      'initialValues': initialValues,
      'hintText': hintText,
      'labelText': labelText,
      'helperText': helperText,
      'errorText': errorText,
      'minSelections': minSelections,
      'maxSelections': maxSelections,
      'isRequired': isRequired,
      'readOnly': readOnly,
      'isDisabled': isDisabled,
      'autoValidate': autoValidate,

      // Display properties
      'displayMode': displayMode.toString().split('.').last,
      'sortMode': sortMode.toString().split('.').last,
      'showSearch': showSearch,
      'searchHintText': searchHintText,
      'showCheckboxes': showCheckboxes,
      'showChips': showChips,
      'showClearButton': showClearButton,
      'showSelectAllButton': showSelectAllButton,
      'selectAllText': selectAllText,
      'clearAllText': clearAllText,

      // Style properties
      'width': width,
      'height': height,
      'maxDropdownHeight': maxDropdownHeight,
      'borderWidth': borderWidth,
      'focusedBorderWidth': focusedBorderWidth,
      'errorBorderWidth': errorBorderWidth,
      'borderRadius': borderRadius,
      'chipBorderRadius': chipBorderRadius,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'dropdownElevation': dropdownElevation,
      'dropdownIconSize': dropdownIconSize,
      'searchIconSize': searchIconSize,
      'clearIconSize': clearIconSize,
      'selectAllIconSize': selectAllIconSize,
      'chipDeleteIconSize': chipDeleteIconSize,

      // Color properties
      'backgroundColor':
          backgroundColor != null ? '#${backgroundColor!.toHexString()}' : null,
      'borderColor':
          borderColor != null ? '#${borderColor!.toHexString()}' : null,
      'focusedBorderColor': '#${focusedBorderColor.toHexString()}',
      'errorBorderColor': '#${errorBorderColor.toHexString()}',
      'chipBackgroundColor':
          chipBackgroundColor != null
              ? '#${chipBackgroundColor!.toHexString()}'
              : null,
      'chipTextColor':
          chipTextColor != null ? '#${chipTextColor!.toHexString()}' : null,
      'chipDeleteIconColor':
          chipDeleteIconColor != null
              ? '#${chipDeleteIconColor!.toHexString()}'
              : null,
      'optionTextColor':
          optionTextColor != null ? '#${optionTextColor!.toHexString()}' : null,
      'selectedOptionTextColor':
          selectedOptionTextColor != null
              ? '#${selectedOptionTextColor!.toHexString()}'
              : null,
      'labelColor': labelColor != null ? '#${labelColor!.toHexString()}' : null,
      'hintColor': hintColor != null ? '#${hintColor!.toHexString()}' : null,
      'helperColor':
          helperColor != null ? '#${helperColor!.toHexString()}' : null,
      'errorColor': errorColor != null ? '#${errorColor!.toHexString()}' : null,
      'searchTextColor':
          searchTextColor != null ? '#${searchTextColor!.toHexString()}' : null,
      'optionBackgroundColor':
          optionBackgroundColor != null
              ? '#${optionBackgroundColor!.toHexString()}'
              : null,
      'selectedOptionBackgroundColor':
          '#${selectedOptionBackgroundColor.toHexString()}',
      'dropdownBackgroundColor':
          dropdownBackgroundColor != null
              ? '#${dropdownBackgroundColor!.toHexString()}'
              : null,
      'dropdownIconColor':
          dropdownIconColor != null
              ? '#${dropdownIconColor!.toHexString()}'
              : null,
      'searchIconColor':
          searchIconColor != null ? '#${searchIconColor!.toHexString()}' : null,
      'clearIconColor':
          clearIconColor != null ? '#${clearIconColor!.toHexString()}' : null,
      'selectAllIconColor':
          selectAllIconColor != null
              ? '#${selectAllIconColor!.toHexString()}'
              : null,
      'shadowColor':
          shadowColor != null ? '#${shadowColor!.toHexString()}' : null,
      'hoverColor': hoverColor != null ? '#${hoverColor!.toHexString()}' : null,
      'hoverBorderColor': '#${hoverBorderColor.toHexString()}',
      'focusColor': focusColor != null ? '#${focusColor!.toHexString()}' : null,

      // Advanced interaction properties
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,

      // Animation properties
      'hasAnimation': hasAnimation,
      'animationDuration': animationDuration.inMilliseconds,
      'animationCurve': animationCurve.toString().split('.').last,

      // Accessibility properties
      'semanticsLabel': semanticsLabel,
      'excludeFromSemantics': excludeFromSemantics,

      // JSON configuration properties
      'useJsonCallbacks': useJsonCallbacks,
      'useJsonStyling': useJsonStyling,
      'useJsonFormatting': useJsonFormatting,
      'useJsonMultiSelectConfig': useJsonMultiSelectConfig,
    };
  }

  @override
  MultiSelectWidgetState createState() => MultiSelectWidgetState();
}

/// The display mode for the multi-select widget.
enum MultiSelectDisplayMode {
  /// Show as a dropdown menu.
  menu,

  /// Show as a dialog.
  dialog,

  /// Show as a bottom sheet.
  bottomSheet,

  /// Show as a chip input field.
  chipInput,
}

/// The sort mode for the options.
enum MultiSelectSortMode {
  /// No sorting.
  none,

  /// Sort alphabetically.
  alphabetical,

  /// Sort selected options first.
  selectedFirst,

  /// Sort selected options last.
  selectedLast,
}

class MultiSelectWidgetState extends State<MultiSelectWidget>
    with SingleTickerProviderStateMixin {
  late List<String> _selectedValues;
  late List<String> _filteredOptions;
  late TextEditingController _searchController;
  bool _isDropdownOpen = false;
  bool _hasFocus = false;
  bool _hasError = false;
  String? _errorText;
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  late FocusNode _focusNode;

  // Animation and interaction state
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isHovered = false;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  @override
  void initState() {
    super.initState();
    _selectedValues = widget.initialValues?.toList() ?? [];
    _filteredOptions = List.from(widget.options);
    _searchController = TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );

    // Initialize with full opacity if not animating
    if (!widget.hasAnimation) {
      _animationController.value = 1.0;
    } else {
      _animationController.forward();
    }

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Sort options based on sort mode
    _sortOptions();

    // Validate initial values if auto-validate is enabled
    if (widget.autoValidate && _selectedValues.isNotEmpty) {
      _validate(_selectedValues);
    }
  }

  @override
  void didUpdateWidget(MultiSelectWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update animation duration and curve if changed
    if (oldWidget.animationDuration != widget.animationDuration) {
      _animationController.duration = widget.animationDuration;
    }

    if (oldWidget.animationCurve != widget.animationCurve) {
      _animation = CurvedAnimation(
        parent: _animationController,
        curve: widget.animationCurve,
      );
    }

    // Handle animation state changes
    if (!oldWidget.hasAnimation && widget.hasAnimation) {
      _animationController.forward(from: 0.0);
    } else if (oldWidget.hasAnimation && !widget.hasAnimation) {
      _animationController.value = 1.0;
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }
  }

  @override
  void dispose() {
    _removeDropdown();
    _searchController.dispose();
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  void _onFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
      if (_hasFocus && widget.onTap != null) {
        widget.onTap!();
      }
    });
  }

  void _sortOptions() {
    switch (widget.sortMode) {
      case MultiSelectSortMode.alphabetical:
        _filteredOptions.sort();
        break;
      case MultiSelectSortMode.selectedFirst:
        _filteredOptions.sort((a, b) {
          final aSelected = _selectedValues.contains(a);
          final bSelected = _selectedValues.contains(b);
          if (aSelected && !bSelected) return -1;
          if (!aSelected && bSelected) return 1;
          return a.compareTo(b);
        });
        break;
      case MultiSelectSortMode.selectedLast:
        _filteredOptions.sort((a, b) {
          final aSelected = _selectedValues.contains(a);
          final bSelected = _selectedValues.contains(b);
          if (aSelected && !bSelected) return 1;
          if (!aSelected && bSelected) return -1;
          return a.compareTo(b);
        });
        break;
      case MultiSelectSortMode.none:
        // No sorting needed
        break;
    }
  }

  bool _validate(List<String> values) {
    if (widget.validator != null) {
      final isValid = widget.validator!(values);
      setState(() {
        _hasError = !isValid;
        _errorText = isValid ? null : (widget.errorText ?? 'Invalid selection');
      });
      return isValid;
    } else if (widget.isRequired && values.isEmpty) {
      setState(() {
        _hasError = true;
        _errorText = 'Please select at least one option';
      });
      return false;
    } else if (widget.minSelections != null &&
        values.length < widget.minSelections!) {
      setState(() {
        _hasError = true;
        _errorText = 'Please select at least ${widget.minSelections} options';
      });
      return false;
    } else if (widget.maxSelections != null &&
        values.length > widget.maxSelections!) {
      setState(() {
        _hasError = true;
        _errorText =
            'Please select no more than ${widget.maxSelections} options';
      });
      return false;
    } else {
      setState(() {
        _hasError = false;
        _errorText = null;
      });
      return true;
    }
  }

  void _toggleDropdown() {
    if (widget.isDisabled || widget.readOnly) return;

    setState(() {
      _isDropdownOpen = !_isDropdownOpen;
      if (_isDropdownOpen) {
        _hasFocus = true;
        _showDropdown();
      } else {
        _hasFocus = false;
        _removeDropdown();
      }
    });
  }

  void _showDropdown() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            left: offset.dx,
            top: offset.dy, // Removed + size.height to remove top space
            width: widget.width ?? size.width,
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: Offset(0.0, 48), // <-- Add space below multiselect
              child: Material(
                elevation: widget.dropdownElevation,
                borderRadius: BorderRadius.circular(4),
                color: Colors.white ?? Theme.of(context).cardColor,
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight:
                        widget.maxDropdownHeight ??
                        _getResponsiveDropdownHeight(context),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.showSearch) _buildSearchField(),
                      if (widget.showSelectAllButton) _buildSelectAllButton(),
                      Flexible(
                        child: ListView.builder(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemCount: _filteredOptions.length,
                          itemBuilder: (context, index) {
                            final option = _filteredOptions[index];
                            final isSelected = _selectedValues.contains(option);
                            return _buildOptionItem(option, isSelected);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeDropdown() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _filterOptions(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredOptions = List.from(widget.options);
      } else {
        _filteredOptions =
            widget.options
                .where(
                  (option) =>
                      option.toLowerCase().contains(query.toLowerCase()),
                )
                .toList();
      }
      _sortOptions();
    });
    _updateOverlay();
  }

  void _updateOverlay() {
    if (_overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  void _toggleOption(String option) {
    setState(() {
      if (_selectedValues.contains(option)) {
        _selectedValues.remove(option);
      } else {
        // Check if adding this option would exceed the maximum selections
        if (widget.maxSelections != null &&
            _selectedValues.length >= widget.maxSelections!) {
          // Show error or handle max selections reached
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'You can select a maximum of ${widget.maxSelections} options',
              ),
              duration: const Duration(seconds: 2),
            ),
          );
          return;
        }
        _selectedValues.add(option);
      }

      _sortOptions();

      if (widget.autoValidate) {
        _validate(_selectedValues);
      }

      if (widget.onChanged != null) {
        widget.onChanged!(_selectedValues);
      }
    });
    _updateOverlay();
  }

  void _selectAll() {
    setState(() {
      if (widget.maxSelections != null &&
          widget.options.length > widget.maxSelections!) {
        // If max selections is set and less than total options, select up to max
        _selectedValues = List.from(widget.options.take(widget.maxSelections!));

        // Show warning about max selections
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Selected ${widget.maxSelections} options (maximum allowed)',
            ),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        // Otherwise select all options
        _selectedValues = List.from(widget.options);
      }

      _sortOptions();

      if (widget.autoValidate) {
        _validate(_selectedValues);
      }

      if (widget.onChanged != null) {
        widget.onChanged!(_selectedValues);
      }

      if (widget.onSelectAll != null) {
        widget.onSelectAll!();
      }
    });
    _updateOverlay();
  }

  void _clearAll() {
    setState(() {
      _selectedValues.clear();

      if (widget.autoValidate) {
        _validate(_selectedValues);
      }

      if (widget.onChanged != null) {
        widget.onChanged!(_selectedValues);
      }

      if (widget.onClear != null) {
        widget.onClear!();
      }
    });
    _updateOverlay();
  }

  void _removeChip(String option) {
    setState(() {
      _selectedValues.remove(option);

      if (widget.autoValidate) {
        _validate(_selectedValues);
      }

      if (widget.onChanged != null) {
        widget.onChanged!(_selectedValues);
      }
    });
  }

  Widget _buildSearchField() {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: widget.searchHintText,
          prefixIcon: Icon(
            widget.searchIcon,
            size: widget.searchIconSize,
            color: widget.searchIconColor ?? const Color(0xFF0058FF),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(
              color: Color(0xFFE6E6E6), // Blue border when not focused
              width: widget.borderWidth,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(
              color: Color(0xFF0058FF), // Red border when focused
              width: widget.borderWidth,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12.0,
            vertical: 8.0,
          ),
          isDense: true,
        ),
        style:
            widget.searchTextStyle ??
            TextStyle(
              color:
                  widget.searchTextColor ?? theme.textTheme.bodyMedium?.color,
              fontSize: _getResponsiveFontSize(context),
            ),
        onChanged: _filterOptions,
      ),
    );
  }

  Widget _buildSelectAllButton() {
    final theme = Theme.of(context);
    final double responsiveFontSize = _getResponsiveFontSize(context);

    return Padding(
      //padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton.icon(
            icon: Icon(
              widget.selectAllIcon,
              size: widget.selectAllIconSize,
              color: widget.selectAllIconColor ?? const Color(0xFF0058FF),
            ),
            label: Text(
              widget.selectAllText,
              style: TextStyle(
                color: Colors.black,
                fontSize: responsiveFontSize - 1.0,
              ),
            ),
            onPressed: _selectAll,
          ),
          TextButton.icon(
            icon: Icon(
              widget.clearIcon,
              size: widget.clearIconSize,
              color: widget.clearIconColor ?? Colors.red,
            ),
            label: Text(
              widget.clearAllText,
              style: TextStyle(
                color: Colors.black,
                fontSize: responsiveFontSize - 1.0,
              ),
            ),
            onPressed: _clearAll,
          ),
        ],
      ),
    );
  }

  Widget _buildOptionItem(String option, bool isSelected) {
    final theme = Theme.of(context);
    final double responsiveFontSize = _getResponsiveFontSize(context);

    final optionStyle =
        isSelected
            ? (widget.selectedOptionTextStyle ??
                TextStyle(
                  //color: widget.selectedOptionTextColor ?? theme.primaryColor,
                  color: Color(0xFF0058FF), // selected text color
                  fontWeight: FontWeight.w600,
                  fontSize: responsiveFontSize,
                ))
            : (widget.optionTextStyle ??
                TextStyle(
                  color:
                      widget.optionTextColor ??
                      theme.textTheme.bodyMedium?.color,
                  fontSize: responsiveFontSize,
                ));

    // final backgroundColor =
    //     isSelected ? (widget.selectedOptionBackgroundColor ??
    //             theme.primaryColor.withAlpha(90))
    //         : (widget.optionBackgroundColor ?? Colors.transparent);
    final backgroundColor =
        isSelected
            ? widget.selectedOptionBackgroundColor.withValues(alpha: 0.1)
            : (widget.optionBackgroundColor ?? Colors.transparent);

    return InkWell(
      onTap: () => _toggleOption(option),
      child: Container(
        padding: widget.optionPadding,
        color: backgroundColor,
        child: Row(
          children: [
            if (widget.showCheckboxes) ...[
              Checkbox(
                value: isSelected,
                onChanged: (_) => _toggleOption(option),
                //activeColor: theme.primaryColor, 
                activeColor: Color(0xFF0058FF), // Change to your desired color
                // checkColor: Colors.white, // Optional: change checkmark color
                side: BorderSide(
                  color: Color(0xFFCCCCCC), // Light gray border for normal state
                  width: 1.5,
                ),
              ),
              const SizedBox(width: 8),
            ],
            Expanded(child: Text(option, style: optionStyle)),
          ],
        ),
      ),
    );
  }

  Widget _buildChips() {
    final double responsiveFontSize = _getResponsiveFontSize(context);

    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0, 
      children:
          _selectedValues.map((option) {
            return Chip(
              label: Text( 
                option,
                style: TextStyle(
                  //color: widget.chipTextColor ?? theme.textTheme.bodyMedium?.color,
                  color: Colors.white,
                  fontSize:
                      responsiveFontSize - 1.0, // Slightly smaller for chips
                ),
              ),
              // backgroundColor: widget.chipBackgroundColor ??   theme.chipTheme.backgroundColor ??
              //     theme.primaryColor.withAlpha(26),
              backgroundColor: Color(0xFF0058FF),
              padding: widget.chipPadding,
              deleteIcon: Icon(
                widget.chipDeleteIcon,
                size: widget.chipDeleteIconSize,
                //color:  widget.chipDeleteIconColor ??  theme.colorScheme.onSurface.withAlpha(179),
                color: Colors.white,
              ),
              onDeleted:
                  widget.readOnly || widget.isDisabled
                      ? null
                      : () => _removeChip(option),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
                side: const BorderSide(
                  // Explicitly remove border
                  color: Colors.transparent,
                  width: 0,
                ),
              ),
            );
          }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Get responsive font size
    final double responsiveFontSize = _getResponsiveFontSize(context);

    // Define text styles with responsive font sizes
    final TextStyle effectiveLabelStyle =
        widget.labelStyle ??
        theme.textTheme.bodySmall?.copyWith(fontSize: responsiveFontSize) ??
        TextStyle(fontSize: responsiveFontSize);

    final TextStyle effectiveHintStyle =
        widget.hintStyle ??
        theme.textTheme.bodySmall?.copyWith(
          color: theme.hintColor,
          fontSize: responsiveFontSize,
        ) ??
        TextStyle(color: theme.hintColor, fontSize: responsiveFontSize);

    final TextStyle effectiveHelperStyle =
        widget.helperStyle ??
        theme.textTheme.bodySmall?.copyWith(
          fontSize: responsiveFontSize - 1.0,
        ) ??
        TextStyle(fontSize: responsiveFontSize - 1.0);

    final TextStyle effectiveErrorStyle =
        widget.errorStyle ??
        theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.error,
          fontSize: responsiveFontSize - 1.0,
        ) ??
        TextStyle(
          color: theme.colorScheme.error,
          fontSize: responsiveFontSize - 1.0,
        );

    // Apply colors if specified
    final TextStyle finalLabelStyle = effectiveLabelStyle.copyWith(
      color: widget.labelColor ?? effectiveLabelStyle.color,
    );

    final TextStyle finalHintStyle = effectiveHintStyle.copyWith(
      color: widget.hintColor ?? effectiveHintStyle.color,
    );

    final TextStyle finalHelperStyle = effectiveHelperStyle.copyWith(
      color: widget.helperColor ?? effectiveHelperStyle.color,
    );

    final TextStyle finalErrorStyle = effectiveErrorStyle.copyWith(
      color: widget.errorColor ?? effectiveErrorStyle.color,
    );

    // Define border colors
    //final Color effectiveBorderColor = widget.borderColor ?? theme.dividerColor;
    final Color effectiveBorderColor = const Color(0xFFCCCCCC);
    final Color effectiveFocusedBorderColor = widget.focusedBorderColor;
    final Color effectiveErrorBorderColor = widget.errorBorderColor;

    // Define icon colors with hover and focus states
    final Color defaultIconColor = const Color(0xFFCCCCCC);
    final Color hoverFocusIconColor = const Color(
      0xFF0058FF,
    ); // Blue for hover/focus

    final Color effectiveDropdownIconColor = _getIconColor(
      widget.dropdownIconColor ?? defaultIconColor ?? defaultIconColor,
      hoverFocusIconColor,
    );

    final Color effectiveClearIconColor = _getIconColor(
      widget.clearIconColor ?? theme.colorScheme.error,
      hoverFocusIconColor,
    );

    Color borderColor = Colors.white;
    if (_hasError) {
      borderColor = effectiveErrorBorderColor;
    } else if (_hasFocus) {
      borderColor = effectiveFocusedBorderColor;
    } else if (_isHovered) {
      borderColor = widget.hoverBorderColor;
    } else {
      borderColor = effectiveBorderColor;
    }
    // Build the input field
    Widget content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.labelText != null) ...[
          // Text(widget.labelText!, style: finalLabelStyle),
          Text(
            widget.labelText!,
            style:
                widget.labelStyle ??
                TextStyle(
                  fontSize: _getResponsiveFontSize(context),
                  fontWeight: FontWeight.w500,
                ),
          ),
              SizedBox(height: _getResponsiveBoxsize(context)),
        ],
        CompositedTransformTarget(
          link: _layerLink,
          child: GestureDetector(
            onTap:
                widget.isDisabled || widget.readOnly ? null : _toggleDropdown,
            onDoubleTap:
                widget.isDisabled || widget.readOnly
                    ? null
                    : widget.onDoubleTap != null
                    ? () {
                      // Execute onDoubleTap callback if defined in JSON
                      if (widget.useJsonCallbacks &&
                          widget.jsonCallbacks != null &&
                          widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                        _executeJsonCallback('onDoubleTap');
                      }

                      // Call standard callback
                      widget.onDoubleTap!();
                    }
                    : null,
            onLongPress:
                widget.isDisabled || widget.readOnly
                    ? null
                    : widget.onLongPress != null
                    ? () {
                      // Execute onLongPress callback if defined in JSON
                      if (widget.useJsonCallbacks &&
                          widget.jsonCallbacks != null &&
                          widget.jsonCallbacks!.containsKey('onLongPress')) {
                        _executeJsonCallback('onLongPress');
                      }

                      // Call standard callback
                      widget.onLongPress!();
                    }
                    : null,
            child: Container(
              width: widget.width,
              //height: widget.height,
              height: _getResponsiveHeight(context),
              margin: widget.margin,
              decoration: BoxDecoration(
                color:
                    widget.isDisabled
                        ? theme.disabledColor.withAlpha(26) // 0.1 opacity
                        : widget.backgroundColor,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: borderColor,

                  // _hasError
                  //     ? effectiveErrorBorderColor
                  //     : (_hasFocus
                  //         ? effectiveFocusedBorderColor
                  //         : effectiveBorderColor),
                  // width:
                  //     _hasError
                  //         ? widget.errorBorderWidth
                  //         : (_hasFocus
                  //             ? widget.focusedBorderWidth
                  //             : widget.borderWidth),
                  width: _hasError ? 1.0 : (_hasFocus ? 1 : 1.0),
                ),
                boxShadow:
                    widget.hasShadow
                        ? [
                          BoxShadow(
                            color: widget.shadowColor ?? Colors.transparent,
                            blurRadius: widget.elevation,
                            offset: Offset(0, widget.elevation / 2),
                          ),
                        ]
                        : null,
              ),
              padding: widget.padding,
              child: Row(
                children: [
                  Expanded(
                    child:
                        widget.showChips && _selectedValues.isNotEmpty
                            ? _buildChips()
                            : Text(
                              _selectedValues.isEmpty
                                  ? widget.hintText ?? 'Select options'
                                  : _selectedValues.join(', '),
                              style:
                                  _selectedValues.isEmpty
                                      ? finalHintStyle
                                      : null,
                              overflow: TextOverflow.ellipsis,
                            ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.showClearButton &&
                          _selectedValues.isNotEmpty) ...[
                        IconButton(
                          icon: Icon(
                            Icons.clear,
                            size: 0, // Or size: widget.clearIconSize ?? 0
                            color: Colors.transparent,
                            // widget.clearIcon,
                            // size: widget.clearIconSize,
                            // color:
                            //     widget.isDisabled
                            //         ? theme.disabledColor
                            //         : effectiveClearIconColor,
                          ),
                          onPressed:
                              widget.isDisabled || widget.readOnly
                                  ? null
                                  : () {
                                    // Execute onClear callback if defined in JSON
                                    if (widget.useJsonCallbacks &&
                                        widget.jsonCallbacks != null &&
                                        widget.jsonCallbacks!.containsKey(
                                          'onClear',
                                        )) {
                                      _executeJsonCallback('onClear');
                                    }

                                    _clearAll();
                                  },
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(
                            minWidth: 0,
                            minHeight: 0,
                          ),
                        ),
                      ],
                      Icon(
                        widget.dropdownIcon,
                        size: widget.dropdownIconSize,
                        color:
                            widget.isDisabled
                                ? theme.disabledColor
                                : effectiveDropdownIconColor,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        if (_errorText != null) ...[
          const SizedBox(height: 4),
          Text(_errorText!, style: finalErrorStyle),
        ] else if (widget.helperText != null) ...[
          const SizedBox(height: 4),
          Text(widget.helperText!, style: finalHelperStyle),
        ],
      ],
    );

    // Apply animation if needed
    if (widget.hasAnimation) {
      content = FadeTransition(opacity: _animation, child: content);
    }

    // Apply advanced interaction properties - always apply MouseRegion for hover border color
    content = MouseRegion(
      onEnter: (event) {
        setState(() {
          _isHovered = true;
        });
        if (widget.onHover != null) {
          widget.onHover!(true);
        }
      },
      onExit: (event) {
        setState(() {
          _isHovered = false;
        });
        if (widget.onHover != null) {
          widget.onHover!(false);
        }
      },
      child: content,
    );

    // Apply focus handling
    if (widget.autofocus) {
      content = Focus(
        focusNode: _focusNode,
        autofocus: widget.autofocus,
        onFocusChange: widget.onFocus,
        child: content,
      );
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }

  double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}

  double _getResponsiveDropdownHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Base dropdown height based on screen size
    double baseDropdownHeight;
    if (screenWidth > 1920) {
      baseDropdownHeight = 400.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      baseDropdownHeight = 350.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      baseDropdownHeight = 320.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      baseDropdownHeight = 280.0; // Small (768-1024px)
    } else {
      baseDropdownHeight = 250.0; // Default for very small screens
    }

    // Limit dropdown height to 60% of screen height to prevent overflow
    final maxAllowedHeight = screenHeight * 0.6;
    return baseDropdownHeight > maxAllowedHeight
        ? maxAllowedHeight
        : baseDropdownHeight;
  }

 double _getResponsiveFontSize(BuildContext context) {
final screenWidth = MediaQuery.of(context).size.width;

if (screenWidth > 1920) {
  return 16.0; // Extra Large
} else if (screenWidth >= 1440) {
  return 14.0; // Large
} else if (screenWidth >= 1280) {
  return 12.0; // Medium
} else {
  return 12.0; // Default for very small screens
}
}

  /// Get icon color based on hover and focus states
  Color _getIconColor(Color defaultColor, Color hoverFocusColor) {
    if (_hasError) {
      return const Color(
        0xFFCCCCCC,
      ); // Light grey for error state (highest priority)
    } else if (_hasFocus || _isHovered) {
      return hoverFocusColor; // Blue for hover/focus (high priority)
    } else if (widget.isDisabled) {
      return Colors.grey.withValues(alpha: 0.5); // Disabled state
    } else {
      return defaultColor; // Default color (lowest priority)
    }
  }
  
  double _getResponsiveBoxsize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 8.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 8.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 6.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 4.0; // Small (768-1024px)
  } else {
    return 4.0; // Default for very small screens
  }
}

}
