import 'package:flutter/material.dart';
import 'dart:convert';
import '../utils/callback_interpreter.dart';

/// A configurable dropdown widget that supports various customization options.
class DropdownWidget extends StatefulWidget {
  // Basic properties
  final List<dynamic> options;
  final dynamic initialValue;
  final String? hint;
  final String? label;
  final String? helperText;
  final String? errorText;
  final bool isExpanded;
  final bool isDense;
  final bool isRequired;
  final bool showTitle;

  // Data configuration properties
  final String? displayField;
  final String? valueField;
  final bool allowComplexObjects;
  final Function(dynamic)? displayValueMapper;
  final Function(dynamic)? valueMapper;
  final bool useNestedData;
  final String? nestedDataPath;
  final Function(dynamic)? dataTransformer;
  final Function(List<dynamic>)? sortFunction;
  final Function(dynamic, String?)? filterFunction;

  // Appearance properties
  final Color textColor;
  final Color backgroundColor;
  final Color dropdownColor;
  final Color iconColor;
  final Color borderColor;
  final double borderWidth;
  final double borderRadius;
  final bool hasBorder;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isCompact;
  final bool hasShadow;
  final double elevation;
  final bool isDarkTheme;
  final TextAlign textAlign;

  // Behavior properties
  final bool isReadOnly;
  final bool isDisabled;
  final bool autofocus;
  final bool hasAnimation;

  // Icon properties
  final bool showIcon;
  final IconData? icon;
  final bool showPrefixIcon;
  final IconData? prefixIcon;
  final bool showSuffixIcon;
  final IconData? suffixIcon;

  // Layout properties
  final double width;
  final double height;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;

  // Callback
  final Function(String?)? onChanged;

  // Advanced interaction properties
  final void Function(bool)? onHover;
  final void Function(bool)? onFocus;
  final Color? hoverColor;
  final Color? focusColor;
  final bool enableFeedback;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  // JSON configuration properties
  final Map<String, dynamic>? jsonCallbacks;
  final bool useJsonCallbacks;
  final Map<String, dynamic>? callbackState;
  final Map<String, Function>? customCallbackHandlers;
  final Map<String, dynamic>? jsonConfig;
  final bool useJsonValidation;
  final bool useJsonStyling;
  final bool useJsonFormatting;

  const DropdownWidget({
    super.key,
    this.options = const ["Option 1", "Option 2", "Option 3"],
    this.initialValue,
    this.hint,
    this.label,
    this.helperText,
    this.errorText,
    this.isExpanded = true,
    this.isDense = false,
    this.isRequired = false,
    this.showTitle = true,
    // Data configuration properties
    this.displayField,
    this.valueField,
    this.allowComplexObjects = false,
    this.displayValueMapper,
    this.valueMapper,
    this.useNestedData = false,
    this.nestedDataPath,
    this.dataTransformer,
    this.sortFunction,
    this.filterFunction,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.dropdownColor = Colors.white,
    this.iconColor = Colors.black,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isCompact = false,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isDarkTheme = false,
    this.textAlign = TextAlign.start,
    this.isReadOnly = false,
    this.isDisabled = false,
    this.autofocus = false,
    this.hasAnimation = false,
    this.showIcon = true,
    this.icon,
    this.showPrefixIcon = false,
    this.prefixIcon,
    this.showSuffixIcon = false,
    this.suffixIcon,
    this.width = double.infinity,
    this.height = 0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.onChanged,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonValidation = false,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
  });

  @override
  State<DropdownWidget> createState() => _DropdownWidgetState();
}

class _DropdownWidgetState extends State<DropdownWidget> with SingleTickerProviderStateMixin {
  dynamic _selectedValue;
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Map to store dynamic state for callbacks
  Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  // List to store processed options
  List<dynamic> _processedOptions = [];

  // Map to store display text for each value
  final Map<dynamic, String> _displayTextMap = {};

  // Flag to track validation state
  bool _isValid = true;

  // Search text for filtering
  String? _searchText;

  // Hover state management
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();

    // Process options
    _processOptions();

    // Set initial value
    _selectedValue = widget.initialValue;

    // Initialize callback state
    _callbackState = widget.callbackState != null
        ? Map<String, dynamic>.from(widget.callbackState!)
        : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);

      // Apply initial JSON validation if enabled
      if (widget.useJsonValidation) {
        _applyJsonValidation();
      }

      // Apply initial JSON styling if enabled
      if (widget.useJsonStyling) {
        _applyJsonStyling();
      }

      // Apply initial JSON formatting if enabled
      if (widget.useJsonFormatting) {
        _applyJsonFormatting();
      }
    }

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Create animation
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Start animation if needed
    if (widget.hasAnimation) {
      _animationController.forward();
    }

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  /// Process options to handle complex objects and nested data
  void _processOptions() {
    _processedOptions = List<dynamic>.from(widget.options);

    // Apply data transformer if provided
    if (widget.dataTransformer != null) {
      try {
        final transformed = widget.dataTransformer!(_processedOptions);
        if (transformed is List) {
          _processedOptions = transformed;
        }
      } catch (e) {
        debugPrint('Error applying data transformer: $e');
      }
    }

    // Apply sorting if provided
    if (widget.sortFunction != null) {
      try {
        widget.sortFunction!(_processedOptions);
      } catch (e) {
        debugPrint('Error applying sort function: $e');
      }
    }

    // Apply filtering if provided and search text is not empty
    if (widget.filterFunction != null && _searchText != null && _searchText!.isNotEmpty) {
      try {
        _processedOptions = _processedOptions.where((option) =>
          widget.filterFunction!(option, _searchText) == true
        ).toList();
      } catch (e) {
        debugPrint('Error applying filter function: $e');
      }
    }

    // Build display text map for complex objects
    _buildDisplayTextMap();
  }

  /// Build a map of display text for each option value
  void _buildDisplayTextMap() {
    _displayTextMap.clear();

    for (final option in _processedOptions) {
      if (option is String) {
        // Simple string option
        _displayTextMap[option] = option;
      } else if (option is Map && widget.allowComplexObjects) {
        // Complex object option
        final displayField = widget.displayField ?? 'label';
        final valueField = widget.valueField ?? 'value';

        dynamic value;
        String displayText;

        // Extract value
        if (widget.valueMapper != null) {
          try {
            value = widget.valueMapper!(option);
          } catch (e) {
            debugPrint('Error applying value mapper: $e');
            value = option[valueField] ?? option;
          }
        } else {
          value = option[valueField] ?? option;
        }

        // Extract display text
        if (widget.displayValueMapper != null) {
          try {
            displayText = widget.displayValueMapper!(option).toString();
          } catch (e) {
            debugPrint('Error applying display value mapper: $e');
            displayText = option[displayField]?.toString() ?? value.toString();
          }
        } else {
          displayText = option[displayField]?.toString() ?? value.toString();
        }

        _displayTextMap[value] = displayText;
      } else {
        // Fallback for other types
        _displayTextMap[option] = option.toString();
      }
    }
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current value
      _callbackState['selectedValue'] = _selectedValue;

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        callbackValue = data;
        _callbackState['data'] = data.toString();
      } else {
        callbackValue = _selectedValue;
      }

      // Execute the callback using the CallbackInterpreter
      try {
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON validation rules to the current value
  void _applyJsonValidation() {
    if (_parsedJsonConfig == null || !widget.useJsonValidation) return;

    // Example: Apply validation rules
    if (_parsedJsonConfig!.containsKey('validationRules')) {
      final rules = _parsedJsonConfig!['validationRules'];

      if (rules is Map<String, dynamic>) {
        // Apply required validation
        if (rules.containsKey('required') && rules['required'] == true) {
          if (_selectedValue == null || _selectedValue!.isEmpty) {
            _isValid = false;
            return;
          }
        }

        // Apply allowed values validation
        if (rules.containsKey('allowedValues') && rules['allowedValues'] is List) {
          final allowedValues = List<String>.from((rules['allowedValues'] as List).map((e) => e.toString()));
          if (_selectedValue != null && !allowedValues.contains(_selectedValue)) {
            _isValid = false;
            return;
          }
        }

        // Apply disallowed values validation
        if (rules.containsKey('disallowedValues') && rules['disallowedValues'] is List) {
          final disallowedValues = List<String>.from((rules['disallowedValues'] as List).map((e) => e.toString()));
          if (_selectedValue != null && disallowedValues.contains(_selectedValue)) {
            _isValid = false;
            return;
          }
        }

        // Apply custom validation
        if (rules.containsKey('custom') && rules['custom'] is String) {
          final customRule = rules['custom'] as String;

          // Example: Check if value starts with a specific prefix
          if (customRule.startsWith('startsWith:')) {
            final prefix = customRule.substring('startsWith:'.length);
            if (_selectedValue != null && !_selectedValue!.startsWith(prefix)) {
              _isValid = false;
              return;
            }
          }

          // Example: Check if value ends with a specific suffix
          if (customRule.startsWith('endsWith:')) {
            final suffix = customRule.substring('endsWith:'.length);
            if (_selectedValue != null && !_selectedValue!.endsWith(suffix)) {
              _isValid = false;
              return;
            }
          }
        }
      }
    }

    _isValid = true;
  }

  /// Applies JSON styling to the widget
  void _applyJsonStyling() {
    if (_parsedJsonConfig == null || !widget.useJsonStyling) return;

    // This would be implemented to apply dynamic styling from JSON
    // Not fully implemented in this example
  }

  /// Applies JSON formatting to the current value
  void _applyJsonFormatting() {
    if (_parsedJsonConfig == null || !widget.useJsonFormatting) return;

    // This would be implemented to apply dynamic formatting from JSON
    // Not fully implemented in this example
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width; 
    if (screenWidth > 1920) {
      return 16.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 14.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
   } else {
      return 12.0; // Default for very small screens
    }
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor = widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor = widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;
    final effectiveDropdownColor = widget.isDarkTheme ? Colors.grey.shade700 : widget.dropdownColor;
    final effectiveIconColor = widget.isDarkTheme ? Colors.white : widget.iconColor;
    final effectiveBorderColor = widget.isDarkTheme ? Color(0xFFCCCCCC) : widget.borderColor;

    // Define hover colors
    final hoverBackgroundColor = widget.hoverColor ?? Colors.white;
    final hoverBorderColor = Color(0xFF0058FF);
    final hoverIconColor = Color(0xFF0058FF);
    final defaultIconColor = Color(0xFFCCCCCC);

    // Get display text for selected value
    String displayText = widget.hint ?? 'Select Item';
    if (_selectedValue != null) {
      if (_displayTextMap.containsKey(_selectedValue)) {
        displayText = _displayTextMap[_selectedValue]!;
      } else {
        displayText = _selectedValue.toString();
      }
    }

    // Create custom dropdown field
    final customDropdownField = GestureDetector(
      onTap: widget.isDisabled || widget.isReadOnly ? null : () => _showCustomDropdown(context),
      child: Container(
        padding: widget.padding,
        decoration: BoxDecoration(
          color: _isHovered ? hoverBackgroundColor : effectiveBackgroundColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: widget.hasBorder
              ? Border.all(
                  color: _isHovered ? hoverBorderColor : effectiveBorderColor,
                  width: widget.borderWidth,
                )
              : null,
          boxShadow: widget.hasShadow
              ? [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: widget.elevation,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            if (widget.showPrefixIcon)
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Icon(
                  widget.prefixIcon ?? Icons.list,
                  color: effectiveIconColor,
                  size: widget.fontSize * 1.2,
                ),
              ),
            Expanded(
              child: Text(
                displayText,
                style: TextStyle(
                  color: _selectedValue == null ? Colors.grey.shade500 : effectiveTextColor,
                  fontSize: _getResponsiveFontSize(context),
                  fontWeight: widget.fontWeight,
                ),
                textAlign: widget.textAlign,
              ),
            ),
            if (widget.showIcon)
              Padding(
                padding: const EdgeInsets.only(left: 4.0),
                child: Icon(
                  widget.icon ?? Icons.arrow_drop_down,
                  color: _isHovered ? hoverIconColor : defaultIconColor,
                  size: _getResponsiveFontSize(context),
                ),
              ),
          ],
        ),
      ),
    );

    // Apply animation if needed
    final animatedWidget = widget.hasAnimation
        ? FadeTransition(
            opacity: _animation,
            child: customDropdownField,
          )
        : customDropdownField;

    // Apply size constraints and hover detection
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Title/Label display
        if (widget.showTitle && widget.label != null)
          Text(
            widget.label!,
            style: TextStyle(
              fontSize: _getResponsiveFontSize(context),
              fontWeight: FontWeight.w500,
              fontFamily: 'Inter',
              color: widget.isDarkTheme ? Colors.white : Colors.black87,
            ),
          ),
          SizedBox(height: _getResponsiveBoxsize(context)),
        
        // Dropdown field
        Container(
          width: widget.width,
          height: _getResponsiveHeight(context), 
          child: MouseRegion(
            onEnter: (_) {
              setState(() {
                _isHovered = true;
              });
              widget.onHover?.call(true);
              _executeJsonCallback('onHover', true);
            },
            onExit: (_) {
              setState(() {
                _isHovered = false;
              });
              widget.onHover?.call(false);
              _executeJsonCallback('onHover', false);
            },
            child: animatedWidget,
          ),
        ),
        
        // Helper text
        if (widget.helperText != null)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              widget.helperText!,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: widget.fontSize * 0.8,
              ),
            ),
          ),
        
        // Error text
        if (widget.errorText != null)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text(
              widget.errorText!,
              style: TextStyle(
                color: Colors.red,
                fontSize: widget.fontSize * 0.8,
              ),
            ),
          ),
      ],
    );
  }

  void _showCustomDropdown(BuildContext context) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Stack(
              children: [
                // Invisible barrier to close dropdown
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: Colors.transparent,
                  ),
                ),
                // Custom dropdown overlay
                Positioned(
                  left: position.dx,
                  top: position.dy + size.height + 4,
                  width: size.width,
                  child: Material(
                    elevation: 8,
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      constraints: BoxConstraints(
                        maxHeight: 200,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ListView.builder(
                        padding: EdgeInsets.symmetric(vertical: 8),
                        shrinkWrap: true,
                        itemCount: _processedOptions.length,
                        itemBuilder: (context, index) {
                          final option = _processedOptions[index];
                          
                          // Get the value and display text for this option
                          dynamic value;
                          String displayText;

                          if (option is String) {
                            value = option;
                            displayText = option;
                          } else if (option is Map && widget.allowComplexObjects) {
                            final displayField = widget.displayField ?? 'label';
                            final valueField = widget.valueField ?? 'value';

                            if (widget.valueMapper != null) {
                              try {
                                value = widget.valueMapper!(option);
                              } catch (e) {
                                value = option[valueField] ?? option;
                              }
                            } else {
                              value = option[valueField] ?? option;
                            }

                            if (widget.displayValueMapper != null) {
                              try {
                                displayText = widget.displayValueMapper!(option).toString();
                              } catch (e) {
                                displayText = option[displayField]?.toString() ?? value.toString();
                              }
                            } else {
                              displayText = option[displayField]?.toString() ?? value.toString();
                            }
                          } else {
                            value = option;
                            displayText = option.toString();
                          }

                          final isSelected = _selectedValue == value;

                          return _DropdownOptionItem(
                            value: value,
                            displayText: displayText,
                            isSelected: isSelected,
                            fontSize: _getResponsiveFontSize(context),
                            onTap: () {
                              Navigator.of(context).pop();
                              
                              // Execute onBeforeChange callback if defined in JSON
                              _executeJsonCallback('onBeforeChange', value);

                              setState(() {
                                _selectedValue = value;
                              });

                              // Apply JSON validation if enabled
                              if (widget.useJsonValidation) {
                                _applyJsonValidation();
                              }

                              // Call standard callback
                              if (widget.onChanged != null) {
                                if (value is! String && widget.onChanged is Function(String?)) {
                                  widget.onChanged!(value?.toString());
                                } else {
                                  widget.onChanged!(value);
                                }
                              }

                              // Execute JSON callback
                              _executeJsonCallback('onChanged', value);

                              if (widget.hasAnimation) {
                                _animationController.reset();
                                _animationController.forward();
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
  
double _getResponsiveBoxsize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 8.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 8.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 6.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 4.0; // Small (768-1024px)
  } else {
    return 4.0; // Default for very small screens
  }
}
}

/// A custom dropdown option item widget with hover effects and background colors
class _DropdownOptionItem extends StatefulWidget {
  final dynamic value;
  final String displayText;
  final bool isSelected;
  final double fontSize;
  final VoidCallback onTap;

  const _DropdownOptionItem({
    required this.value,
    required this.displayText,
    required this.isSelected,
    required this.fontSize,
    required this.onTap,
  });

  @override
  State<_DropdownOptionItem> createState() => _DropdownOptionItemState();
}

class _DropdownOptionItemState extends State<_DropdownOptionItem> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    // Define background colors for different states
    Color backgroundColor;
    if (widget.isSelected) {
      // Selected state: Light blue background
      backgroundColor = Color(0xFFf0f5ff);
    } else if (_isHovered) {
      // Hover state: Light gray background
      backgroundColor = Color(0xFFf0f5ff);
    } else {
      // Normal state: Very light gray background
      backgroundColor = Colors.white;
    }

    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
      },
      child: InkWell(
        onTap: widget.onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: backgroundColor,
          ),
          child: Text(
            widget.displayText,
            style: TextStyle(
              color: widget.isSelected ? Color(0xFF0058FF) : Colors.black87,
              fontSize: widget.fontSize,
              fontWeight: widget.isSelected ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 32.0; // Small (768-1024px)
  } else {
    return 32.0; // Default for very small screens
  }
}
double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}